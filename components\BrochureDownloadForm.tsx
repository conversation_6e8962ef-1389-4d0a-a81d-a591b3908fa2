"use client";

import { useState } from "react";
import { <PERSON><PERSON>Button } from "@/components/magicui/shimmer-button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { MagicCard } from "@/components/magicui/magic-card";
import { useTheme } from "next-themes";
import { leadsApi, ApiError } from "@/lib/api";

interface BrochureDownloadFormProps {
  projectName: string;
  projectId: string;
  brochureUrl?: string; // Made optional since we'll get it from API
}

export function BrochureDownloadForm({ projectName, projectId, brochureUrl }: BrochureDownloadFormProps) {
  const { theme } = useTheme();
  const [formSubmitted, setFormSubmitted] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [downloadUrl, setDownloadUrl] = useState<string | null>(brochureUrl || null);
  const [formData, setFormData] = useState({
    fullName: "",
    email: "",
    phone: "",
    location: "",
    interestedIn: "",
    budget: "",
    message: "",
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { id, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [id]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);

    try {
      const response = await leadsApi.submitBrochureDownload(projectId, formData);
      setDownloadUrl(response.brochureUrl);
      setFormSubmitted(true);

      // Auto-download the brochure
      if (response.brochureUrl) {
        window.open(response.brochureUrl, "_blank");
      }
    } catch (error) {
      if (error instanceof ApiError) {
        setError(error.message);
      } else {
        setError("An unexpected error occurred. Please try again.");
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDownload = () => {
    if (downloadUrl) {
      window.open(downloadUrl, "_blank");
    }
  };

  return (
    <Card className="p-0 max-w-sm w-full shadow-none border-none">
      <MagicCard
        gradientColor={theme === "dark" ? "#262626" : "#D9D9D955"}
        className="p-0"
      >
        <CardHeader className="border-b border-border p-4">
          <CardTitle>{formSubmitted ? "Download Brochure" : "Download Request"}</CardTitle>
          <CardDescription>
            {formSubmitted
              ? "Thank you for your interest in " + projectName
              : "Please enter your details to download the brochure for " + projectName}
          </CardDescription>
        </CardHeader>
        <CardContent className="p-4">
          {!formSubmitted ? (
            <form onSubmit={handleSubmit}>
              <div className="grid gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="fullName">Full Name</Label>
                  <Input
                    id="fullName"
                    value={formData.fullName}
                    onChange={handleInputChange}
                    placeholder="John Doe"
                    required
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    placeholder="<EMAIL>"
                    required
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="phone">Phone Number</Label>
                  <Input
                    id="phone"
                    type="tel"
                    value={formData.phone}
                    onChange={handleInputChange}
                    placeholder="+91 9999999999"
                    required
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="location">Location (Optional)</Label>
                  <Input
                    id="location"
                    value={formData.location}
                    onChange={handleInputChange}
                    placeholder="Your city"
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="interestedIn">Interested In (Optional)</Label>
                  <Input
                    id="interestedIn"
                    value={formData.interestedIn}
                    onChange={handleInputChange}
                    placeholder="2BHK, 3BHK, etc."
                  />
                </div>
              </div>
              {error && (
                <div className="text-red-500 text-sm mt-2 p-2 bg-red-50 dark:bg-red-900/20 rounded">
                  {error}
                </div>
              )}
              <div className="mt-4">
                <ShimmerButton
                  type="submit"
                  shimmerColor="#3b82f6"
                  background="rgba(59, 130, 246, 0.9)"
                  className="font-medium w-full"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? "Submitting..." : "Download Brochure"}
                </ShimmerButton>
              </div>
            </form>
          ) : (
            <div className="text-center py-4">
              <p className="mb-4">Your brochure is ready to download</p>
              <ShimmerButton
                onClick={handleDownload}
                shimmerColor="#3b82f6"
                background="rgba(59, 130, 246, 0.9)"
                className="font-medium w-full"
              >
                Download Brochure
              </ShimmerButton>
            </div>
          )}
        </CardContent>
      </MagicCard>
    </Card>
  );
}