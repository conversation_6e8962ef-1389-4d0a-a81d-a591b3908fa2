const mongoose = require('mongoose');
require('dotenv').config();

const User = require('../models/User');
const Amenity = require('../models/Amenity');
const Project = require('../models/Project');
const Testimonial = require('../models/Testimonial');

// Connect to MongoDB
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('✅ MongoDB Connected for seeding');
  } catch (error) {
    console.error('❌ MongoDB connection failed:', error.message);
    process.exit(1);
  }
};

// Seed Users
const seedUsers = async () => {
  try {
    // Check if super admin already exists
    const existingSuperAdmin = await User.findOne({ role: 'superadmin' });
    if (existingSuperAdmin) {
      console.log('Super admin already exists, skipping user seeding');
      return;
    }

    const users = [
      {
        email: '<EMAIL>',
        password: 'admin123456',
        role: 'superadmin',
        firstName: 'Super',
        lastName: 'Admin',
        isActive: true
      },
      {
        email: '<EMAIL>',
        password: 'manager123456',
        role: 'admin',
        firstName: 'Project',
        lastName: 'Manager',
        isActive: true
      }
    ];

    await User.insertMany(users);
    console.log('✅ Users seeded successfully');
  } catch (error) {
    console.error('❌ Error seeding users:', error);
  }
};

// Seed Amenities
const seedAmenities = async () => {
  try {
    const existingAmenities = await Amenity.countDocuments();
    if (existingAmenities > 0) {
      console.log('Amenities already exist, skipping amenity seeding');
      return;
    }

    const amenities = [
      {
        name: '24/7 Security',
        icon: {
          url: '/uploads/amenities/security.svg',
          filename: 'security.svg'
        },
        category: 'security',
        description: 'Round-the-clock security with CCTV surveillance'
      },
      {
        name: 'Swimming Pool',
        icon: {
          url: '/uploads/amenities/pool.svg',
          filename: 'pool.svg'
        },
        category: 'recreational',
        description: 'Modern swimming pool with changing rooms'
      },
      {
        name: 'Fitness Center',
        icon: {
          url: '/uploads/amenities/fitness.svg',
          filename: 'fitness.svg'
        },
        category: 'wellness',
        description: 'Fully equipped gymnasium with modern equipment'
      },
      {
        name: 'Garden Area',
        icon: {
          url: '/uploads/amenities/garden.svg',
          filename: 'garden.svg'
        },
        category: 'recreational',
        description: 'Landscaped gardens with walking paths'
      },
      {
        name: 'Parking Space',
        icon: {
          url: '/uploads/amenities/parking.svg',
          filename: 'parking.svg'
        },
        category: 'parking',
        description: 'Covered parking spaces for residents'
      },
      {
        name: "Children's Play Area",
        icon: {
          url: '/uploads/amenities/playground.svg',
          filename: 'playground.svg'
        },
        category: 'recreational',
        description: 'Safe and fun play area for children'
      },
      {
        name: 'Club House',
        icon: {
          url: '/uploads/amenities/clubhouse.svg',
          filename: 'clubhouse.svg'
        },
        category: 'recreational',
        description: 'Community clubhouse for events and gatherings'
      },
      {
        name: 'Elevator',
        icon: {
          url: '/uploads/amenities/elevator.svg',
          filename: 'elevator.svg'
        },
        category: 'convenience',
        description: 'High-speed elevators in all towers'
      },
      {
        name: 'WiFi',
        icon: {
          url: '/uploads/amenities/wifi.svg',
          filename: 'wifi.svg'
        },
        category: 'connectivity',
        description: 'High-speed internet connectivity'
      },
      {
        name: '24-Hour Power Backup',
        icon: {
          url: '/uploads/amenities/power.svg',
          filename: 'power.svg'
        },
        category: 'convenience',
        description: 'Uninterrupted power supply with backup generators'
      }
    ];

    await Amenity.insertMany(amenities);
    console.log('✅ Amenities seeded successfully');
  } catch (error) {
    console.error('❌ Error seeding amenities:', error);
  }
};

// Seed Projects
const seedProjects = async () => {
  try {
    const existingProjects = await Project.countDocuments();
    if (existingProjects > 0) {
      console.log('Projects already exist, skipping project seeding');
      return;
    }

    // Get the super admin user
    const superAdmin = await User.findOne({ role: 'superadmin' });
    if (!superAdmin) {
      console.log('No super admin found, skipping project seeding');
      return;
    }

    // Get some amenities
    const amenities = await Amenity.find().limit(6);

    const projects = [
      {
        title: 'Laxmi Heights',
        description: 'Laxmi Heights is a premium residential project offering luxury living spaces designed for modern families. Located in the heart of Vesu, Surat, these apartments offer a perfect blend of comfort, convenience, and sophisticated living.',
        location: {
          address: 'Vesu Main Road',
          city: 'Surat',
          state: 'Gujarat',
          pincode: '395007',
          coordinates: {
            lat: 21.1458,
            lng: 72.7775
          }
        },
        category: 'residential',
        status: 'ongoing',
        customTags: ['Premium', 'Luxury', 'Modern'],
        pricing: {
          startingPrice: 45,
          priceUnit: 'lakh',
          priceDetails: 'Starting from ₹45 Lakhs for 2BHK'
        },
        specifications: {
          totalUnits: 64,
          projectArea: '2.5 Acres',
          towers: 2,
          floors: 12,
          parkingSpaces: 80
        },
        coverImage: {
          url: 'https://images.unsplash.com/photo-1523217582562-09d0def993a6?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
          alt: 'Laxmi Heights - Premium Residential Project',
          filename: 'laxmi-heights-cover.jpg'
        },
        amenities: amenities.map(a => a._id),
        seo: {
          metaTitle: 'Laxmi Heights - Premium Apartments in Vesu, Surat',
          metaDescription: 'Discover luxury living at Laxmi Heights, premium residential apartments in Vesu, Surat. Modern amenities, prime location, and affordable pricing.',
          keywords: ['residential apartments', 'Vesu', 'Surat', 'luxury homes', 'premium apartments']
        },
        createdBy: superAdmin._id
      },
      {
        title: 'Laxmi Arcade',
        description: 'Laxmi Arcade is a state-of-the-art commercial complex offering premium retail and office spaces. Strategically located on Ring Road, Surat, it features modern amenities and excellent connectivity.',
        location: {
          address: 'Ring Road',
          city: 'Surat',
          state: 'Gujarat',
          pincode: '395002',
          coordinates: {
            lat: 21.1702,
            lng: 72.8311
          }
        },
        category: 'commercial',
        status: 'completed',
        customTags: ['Commercial', 'Retail', 'Office Space'],
        pricing: {
          startingPrice: 75,
          priceUnit: 'lakh',
          priceDetails: 'Starting from ₹75 Lakhs for retail space'
        },
        specifications: {
          totalUnits: 32,
          projectArea: '1.8 Acres',
          towers: 1,
          floors: 8,
          parkingSpaces: 50
        },
        coverImage: {
          url: 'https://images.unsplash.com/photo-1579632652768-6cb9dcf85912?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
          alt: 'Laxmi Arcade - Premium Commercial Complex',
          filename: 'laxmi-arcade-cover.jpg'
        },
        amenities: amenities.slice(0, 4).map(a => a._id),
        seo: {
          metaTitle: 'Laxmi Arcade - Commercial Complex Ring Road, Surat',
          metaDescription: 'Invest in Laxmi Arcade, premium commercial complex on Ring Road, Surat. Retail and office spaces with modern amenities and prime location.',
          keywords: ['commercial complex', 'Ring Road', 'Surat', 'retail space', 'office space']
        },
        createdBy: superAdmin._id
      }
    ];

    await Project.insertMany(projects);
    console.log('✅ Projects seeded successfully');
  } catch (error) {
    console.error('❌ Error seeding projects:', error);
  }
};

// Seed Testimonials
const seedTestimonials = async () => {
  try {
    const existingTestimonials = await Testimonial.countDocuments();
    if (existingTestimonials > 0) {
      console.log('Testimonials already exist, skipping testimonial seeding');
      return;
    }

    // Get some projects to reference
    const projects = await Project.find().limit(2);
    if (projects.length === 0) {
      console.log('No projects found, skipping testimonial seeding');
      return;
    }

    const testimonials = [
      {
        type: 'text',
        clientName: 'Rajesh Patel',
        clientDesignation: 'Business Owner',
        content: 'Laxmi Developers delivered exactly what they promised. The quality of construction and attention to detail is exceptional. Our family is very happy with our new home.',
        rating: 5,
        projectReference: projects[0]._id,
        displayOrder: 1,
        isActive: true
      },
      {
        type: 'text',
        clientName: 'Priya Sharma',
        clientDesignation: 'Software Engineer',
        content: 'The entire process from booking to possession was smooth and transparent. The amenities are world-class and the location is perfect for our needs.',
        rating: 5,
        projectReference: projects[0]._id,
        displayOrder: 2,
        isActive: true
      },
      {
        type: 'text',
        clientName: 'Amit Kumar',
        clientDesignation: 'Doctor',
        content: 'Excellent build quality and timely delivery. The team was professional and responsive throughout the project. Highly recommend Laxmi Developers.',
        rating: 4,
        projectReference: projects[1] ? projects[1]._id : projects[0]._id,
        displayOrder: 3,
        isActive: true
      },
      {
        type: 'text',
        clientName: 'Sneha Joshi',
        clientDesignation: 'Teacher',
        content: 'Beautiful design and excellent amenities. The project exceeded our expectations in terms of quality and value for money.',
        rating: 5,
        projectReference: projects[1] ? projects[1]._id : projects[0]._id,
        displayOrder: 4,
        isActive: true
      }
    ];

    await Testimonial.insertMany(testimonials);
    console.log('✅ Testimonials seeded successfully');
  } catch (error) {
    console.error('❌ Error seeding testimonials:', error);
  }
};

// Main seeder function
const seedDatabase = async () => {
  try {
    await connectDB();

    console.log('🌱 Starting database seeding...');

    await seedUsers();
    await seedAmenities();
    await seedProjects();
    await seedTestimonials();

    console.log('🎉 Database seeding completed successfully!');

    // Display login credentials
    console.log('\n📋 Default Login Credentials:');
    console.log('Super Admin: <EMAIL> / admin123456');
    console.log('Admin: <EMAIL> / manager123456');

  } catch (error) {
    console.error('❌ Error seeding database:', error);
  } finally {
    await mongoose.connection.close();
    console.log('🔌 Database connection closed');
    process.exit(0);
  }
};

// Run seeder if called directly
if (require.main === module) {
  seedDatabase();
}

module.exports = { seedDatabase };
