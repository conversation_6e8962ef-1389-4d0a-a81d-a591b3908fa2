import { useState, useEffect } from 'react';
import { testimonialsApi, ApiError } from '@/lib/api';

interface Testimonial {
  id: string;
  name: string;
  role: string;
  quote: string;
  avatarSrc?: string;
  type: 'text' | 'video';
  videoSrc?: string;
  thumbnailSrc?: string;
  rating: number;
  project?: any;
  displayOrder: number;
  createdAt: string;
}

interface UseTestimonialsParams {
  type?: 'text' | 'video';
  active?: boolean;
  project?: string;
}

interface UseTestimonialsReturn {
  testimonials: Testimonial[];
  loading: boolean;
  error: string | null;
  refetch: () => void;
}

export function useTestimonials(params: UseTestimonialsParams = {}): UseTestimonialsReturn {
  const [testimonials, setTestimonials] = useState<Testimonial[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchTestimonials = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await testimonialsApi.getAll(params);
      setTestimonials(response.testimonials);
    } catch (err) {
      if (err instanceof ApiError) {
        setError(err.message);
      } else {
        setError('Failed to fetch testimonials');
      }
      setTestimonials([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTestimonials();
  }, [params.type, params.active, params.project]);

  return {
    testimonials,
    loading,
    error,
    refetch: fetchTestimonials
  };
}
