import { useState, useEffect } from 'react';
import { projectsApi, ApiError } from '@/lib/api';

interface Project {
  id: string;
  title: string;
  description: string;
  location: string;
  fullLocation: string;
  category: 'residential' | 'commercial';
  status: 'completed' | 'ongoing' | 'upcoming';
  imageSrc: string;
  href: string;
  pricing: {
    startingPrice: number;
    priceUnit: string;
    priceDetails: string;
  };
  specifications: {
    totalUnits: number;
    projectArea: string;
    towers: number;
    floors: number;
    parkingSpaces: number;
  };
  amenities: any[];
  customTags: string[];
  createdAt: string;
}

interface UseProjectsParams {
  category?: 'residential' | 'commercial';
  status?: 'completed' | 'ongoing' | 'upcoming';
  city?: string;
  search?: string;
  page?: number;
  limit?: number;
}

interface UseProjectsReturn {
  projects: Project[];
  loading: boolean;
  error: string | null;
  pagination: {
    currentPage: number;
    totalPages: number;
    totalProjects: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
  } | null;
  refetch: () => void;
}

export function useProjects(params: UseProjectsParams = {}): UseProjectsReturn {
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState<UseProjectsReturn['pagination']>(null);

  const fetchProjects = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await projectsApi.getAll(params);
      setProjects(response.projects);
      setPagination(response.pagination);
    } catch (err) {
      if (err instanceof ApiError) {
        setError(err.message);
      } else {
        setError('Failed to fetch projects');
      }
      setProjects([]);
      setPagination(null);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchProjects();
  }, [
    params.category,
    params.status,
    params.city,
    params.search,
    params.page,
    params.limit
  ]);

  return {
    projects,
    loading,
    error,
    pagination,
    refetch: fetchProjects
  };
}

// Hook for single project
export function useProject(id: string) {
  const [project, setProject] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchProject = async () => {
    if (!id) return;
    
    try {
      setLoading(true);
      setError(null);
      
      const response = await projectsApi.getById(id);
      setProject(response.project);
    } catch (err) {
      if (err instanceof ApiError) {
        setError(err.message);
      } else {
        setError('Failed to fetch project');
      }
      setProject(null);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchProject();
  }, [id]);

  return {
    project,
    loading,
    error,
    refetch: fetchProject
  };
}
