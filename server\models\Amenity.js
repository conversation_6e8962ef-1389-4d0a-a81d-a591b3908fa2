const mongoose = require('mongoose');

const amenitySchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Amenity name is required'],
    unique: true,
    trim: true,
    maxlength: [100, 'Amenity name cannot exceed 100 characters']
  },
  icon: {
    url: {
      type: String,
      required: [true, 'Icon URL is required']
    },
    filename: {
      type: String,
      required: [true, 'Icon filename is required']
    }
  },
  category: {
    type: String,
    enum: ['recreational', 'security', 'convenience', 'wellness', 'connectivity', 'parking', 'maintenance'],
    default: 'convenience'
  },
  description: {
    type: String,
    maxlength: [500, 'Description cannot exceed 500 characters']
  },
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true
});

// Indexes for performance
amenitySchema.index({ name: 1 });
amenitySchema.index({ category: 1 });
amenitySchema.index({ isActive: 1 });
amenitySchema.index({ name: 'text', description: 'text' });

// Static method to get amenities by category
amenitySchema.statics.getByCategory = function(category) {
  return this.find({ category, isActive: true }).sort({ name: 1 });
};

// Static method to get active amenities
amenitySchema.statics.getActive = function() {
  return this.find({ isActive: true }).sort({ name: 1 });
};

module.exports = mongoose.model('Amenity', amenitySchema);
