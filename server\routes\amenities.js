const express = require('express');
const { body, validationResult, query } = require('express-validator');
const Amenity = require('../models/Amenity');
const { authenticateToken, requireAdmin } = require('../middleware/auth');
const { uploadConfigs, optimizeImage, generateFileUrl } = require('../middleware/upload');

const router = express.Router();

// @route   GET /api/amenities
// @desc    Get all amenities
// @access  Public
router.get('/', [
  query('category').optional().isIn(['recreational', 'security', 'convenience', 'wellness', 'connectivity', 'parking', 'maintenance']),
  query('active').optional().isBoolean()
], async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Invalid query parameters',
        errors: errors.array()
      });
    }

    const { category, active } = req.query;

    // Build filter object
    const filters = {};
    if (category) filters.category = category;
    if (active !== undefined) filters.isActive = active === 'true';
    else filters.isActive = true; // Default to active amenities only

    const amenities = await Amenity.find(filters)
      .select('-__v')
      .sort({ name: 1 });

    res.json({
      success: true,
      data: {
        amenities
      }
    });

  } catch (error) {
    console.error('Get amenities error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error fetching amenities'
    });
  }
});

// @route   GET /api/amenities/categories
// @desc    Get amenities grouped by category
// @access  Public
router.get('/categories', async (req, res) => {
  try {
    const amenitiesByCategory = await Amenity.aggregate([
      { $match: { isActive: true } },
      {
        $group: {
          _id: '$category',
          amenities: {
            $push: {
              _id: '$_id',
              name: '$name',
              icon: '$icon',
              description: '$description'
            }
          },
          count: { $sum: 1 }
        }
      },
      { $sort: { _id: 1 } }
    ]);

    res.json({
      success: true,
      data: {
        categories: amenitiesByCategory
      }
    });

  } catch (error) {
    console.error('Get amenities by category error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error fetching amenities by category'
    });
  }
});

// @route   GET /api/amenities/:id
// @desc    Get single amenity by ID
// @access  Public
router.get('/:id', async (req, res) => {
  try {
    const amenity = await Amenity.findById(req.params.id);

    if (!amenity) {
      return res.status(404).json({
        success: false,
        message: 'Amenity not found'
      });
    }

    res.json({
      success: true,
      data: {
        amenity
      }
    });

  } catch (error) {
    console.error('Get amenity error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error fetching amenity'
    });
  }
});

// @route   POST /api/amenities
// @desc    Create new amenity
// @access  Private (Admin)
router.post('/', [
  authenticateToken,
  requireAdmin,
  uploadConfigs.amenityIcon,
  optimizeImage,
  body('name').notEmpty().withMessage('Amenity name is required'),
  body('category').isIn(['recreational', 'security', 'convenience', 'wellness', 'connectivity', 'parking', 'maintenance']).withMessage('Invalid category'),
  body('description').optional().isLength({ max: 500 }).withMessage('Description cannot exceed 500 characters')
], async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    // Check if icon is uploaded
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'Amenity icon is required'
      });
    }

    // Check if amenity with same name already exists
    const existingAmenity = await Amenity.findOne({ 
      name: new RegExp(`^${req.body.name}$`, 'i') 
    });

    if (existingAmenity) {
      return res.status(400).json({
        success: false,
        message: 'Amenity with this name already exists'
      });
    }

    const amenityData = {
      ...req.body,
      icon: {
        url: generateFileUrl(req, req.file.filename, 'amenities/'),
        filename: req.file.filename
      }
    };

    const amenity = new Amenity(amenityData);
    await amenity.save();

    res.status(201).json({
      success: true,
      message: 'Amenity created successfully',
      data: {
        amenity
      }
    });

  } catch (error) {
    console.error('Create amenity error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error creating amenity'
    });
  }
});

// @route   PUT /api/amenities/:id
// @desc    Update amenity
// @access  Private (Admin)
router.put('/:id', [
  authenticateToken,
  requireAdmin,
  uploadConfigs.amenityIcon,
  optimizeImage,
  body('name').optional().notEmpty().withMessage('Amenity name cannot be empty'),
  body('category').optional().isIn(['recreational', 'security', 'convenience', 'wellness', 'connectivity', 'parking', 'maintenance']).withMessage('Invalid category'),
  body('description').optional().isLength({ max: 500 }).withMessage('Description cannot exceed 500 characters'),
  body('isActive').optional().isBoolean().withMessage('isActive must be a boolean')
], async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const amenity = await Amenity.findById(req.params.id);
    if (!amenity) {
      return res.status(404).json({
        success: false,
        message: 'Amenity not found'
      });
    }

    // Check if name is being changed and if it conflicts with existing amenity
    if (req.body.name && req.body.name !== amenity.name) {
      const existingAmenity = await Amenity.findOne({ 
        name: new RegExp(`^${req.body.name}$`, 'i'),
        _id: { $ne: req.params.id }
      });

      if (existingAmenity) {
        return res.status(400).json({
          success: false,
          message: 'Amenity with this name already exists'
        });
      }
    }

    // Update amenity data
    const updateData = { ...req.body };

    // Handle icon update if new file is uploaded
    if (req.file) {
      updateData.icon = {
        url: generateFileUrl(req, req.file.filename, 'amenities/'),
        filename: req.file.filename
      };
    }

    const updatedAmenity = await Amenity.findByIdAndUpdate(
      req.params.id,
      updateData,
      { new: true, runValidators: true }
    );

    res.json({
      success: true,
      message: 'Amenity updated successfully',
      data: {
        amenity: updatedAmenity
      }
    });

  } catch (error) {
    console.error('Update amenity error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error updating amenity'
    });
  }
});

// @route   DELETE /api/amenities/:id
// @desc    Delete amenity (soft delete)
// @access  Private (Admin)
router.delete('/:id', [
  authenticateToken,
  requireAdmin
], async (req, res) => {
  try {
    const amenity = await Amenity.findById(req.params.id);
    if (!amenity) {
      return res.status(404).json({
        success: false,
        message: 'Amenity not found'
      });
    }

    // Soft delete by setting isActive to false
    amenity.isActive = false;
    await amenity.save();

    res.json({
      success: true,
      message: 'Amenity deleted successfully'
    });

  } catch (error) {
    console.error('Delete amenity error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error deleting amenity'
    });
  }
});

module.exports = router;
