"use client";

import React, { useState, useRef, useEffect } from "react";
import Image from "next/image";
import { ShimmerButton } from "@/components/magicui/shimmer-button";
import { AmenitiesFeatures } from "@/components/AmenitiesFeatures";
import { motion, AnimatePresence, useAnimation, useInView } from "framer-motion";
import { cn } from "@/lib/utils";
import { ChevronLeftIcon, ChevronRightIcon } from "lucide-react";

export interface Amenity {
  id: string;
  name: string;
  icon: React.ReactNode;
}

export interface Specification {
  name: string;
  value: string;
}

interface ProjectDetailsProps {
  title: string;
  description: string;
  location: string;
  status: "ongoing" | "completed" | "upcoming";
  type: "residential" | "commercial";
  imageSrc: string;
  threeDModelUrl?: string; // For future actual 3D model
  specifications: Specification[];
  amenities: Amenity[];
  brochureUrl?: string;
  contactPhone?: string;
  reraNumber?: string;
}

export default function ProjectDetails({
  title,
  description,
  location,
  status,
  type,
  imageSrc,
  threeDModelUrl, // Will be used if/when an actual model URL is provided
  specifications,
  amenities,
  brochureUrl,
  contactPhone,
  reraNumber,
}: ProjectDetailsProps) {
  const [currentSlide, setCurrentSlide] = useState(0); // 0 for image, 1 for 3D view
  const [isLoading3D, setIsLoading3D] = useState(false);
  const [is3DModelLoaded, setIs3DModelLoaded] = useState(false);
  const [progress, setProgress] = useState(0);

  const has3DView = true; // Set to true to always show the 3D view slide option

  const handleLoad3DModel = () => {
    setIsLoading3D(true);
    setProgress(0);
    const interval = setInterval(() => {
      setProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval);
          setIsLoading3D(false);
          setIs3DModelLoaded(true);
          return 100;
        }
        return prev + 10;
      });
    }, 200);
  };

  const slides = ["image"];
  if (has3DView) {
    slides.push("3dview");
  }

  const changeSlide = (direction: number) => {
    setCurrentSlide(prev => {
      const newSlide = prev + direction;
      if (newSlide < 0) return slides.length - 1;
      if (newSlide >= slides.length) return 0;
      return newSlide;
    });
  };
  
  const getStatusColor = () => {
    switch (status) {
      case "ongoing": return "bg-yellow-500/90 hover:bg-yellow-600/90 text-white";
      case "completed": return "bg-green-500/90 hover:bg-green-600/90 text-white";
      case "upcoming": return "bg-blue-500/90 hover:bg-blue-600/90 text-white";
      default: return "bg-gray-500/90 hover:bg-gray-600/90 text-white";
    }
  };

  const getTypeColor = () => {
    switch (type) {
      case "residential": return "bg-purple-500/90 hover:bg-purple-600/90 text-white";
      case "commercial": return "bg-teal-500/90 hover:bg-teal-600/90 text-white";
      default: return "bg-gray-500/90 hover:bg-gray-600/90 text-white";
    }
  };

  return (
    <>
      <section className="py-12 md:py-20 bg-background">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 md:gap-12">
            {/* Project Image / 3D View Slider */}
            <div className="relative h-[350px] sm:h-[450px] md:h-[550px] lg:h-full min-h-[450px] rounded-lg shadow-xl group overflow-hidden">
              <AnimatePresence initial={false} custom={currentSlide}>
                {slides[currentSlide] === "image" && (
                  <motion.div
                    key="image"
                    initial={{ opacity: 0, x: currentSlide === 0 ? 0 : (currentSlide > 0 ? 300 : -300) }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: currentSlide === 0 ? (currentSlide > 0 ? -300: 300) : 0}}
                    transition={{ type: "spring", stiffness: 300, damping: 30 }}
                    className="absolute inset-0"
                  >
                    <Image
                      src={imageSrc}
                      alt={title}
                      fill
                      className="object-cover"
                      priority
                    />
                  </motion.div>
                )}
                {slides[currentSlide] === "3dview" && (
                  <motion.div
                    key="3dview"
                    initial={{ opacity: 0, x: currentSlide === 1 ? 0 : (currentSlide > 1 ? 300 : -300) }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: currentSlide === 1 ? (currentSlide > 0 ? -300 : 300) : 0 }}
                    transition={{ type: "spring", stiffness: 300, damping: 30 }}
                    className="absolute inset-0 flex flex-col items-center justify-center bg-gradient-to-br from-slate-800 via-slate-900 to-black p-8 text-center"
                  >
                    {!is3DModelLoaded && !isLoading3D && (
                      <div className="flex flex-col items-center">
                        <h3 className="text-2xl sm:text-3xl font-semibold text-white mb-3">Interactive 3D Tour</h3>
                        <p className="text-gray-300 mb-6 text-sm sm:text-base max-w-md">Explore the project in immersive 3D. Click below to load the experience.</p>
                        <ShimmerButton
                          onClick={handleLoad3DModel}
                          shimmerColor="#FFFFFFCC"
                          shimmerSize="0.1em"
                          background="rgba(var(--highlight-rgb), 0.9)"
                          className="font-medium px-8 py-3 text-sm sm:text-base rounded-lg shadow-lg hover:shadow-xl transition-all duration-300"
                        >
                          Load 3D Model
                        </ShimmerButton>
                      </div>
                    )}
                    {isLoading3D && (
                      <div className="w-full max-w-xs flex flex-col items-center">
                        <div className="h-3 w-full relative max-w-xs rounded-full overflow-hidden bg-gray-700/50 mb-2.5">
                          <motion.div 
                            className="h-full bg-highlight absolute"
                            initial={{ width: "0%" }}
                            animate={{ width: `${progress}%` }}
                            transition={{ duration: 0.2, ease: "linear"}}
                          />
                        </div>
                        <p className="text-white text-xs sm:text-sm">Loading 3D Model... {progress}%</p>
                      </div>
                    )}
                    {is3DModelLoaded && (
                      <div className="text-white text-xl sm:text-2xl font-semibold">
                        3D Model will be added here.
                        {/* Replace above with actual <iframe> or 3D component when URL is available */}
                        {/* e.g., <iframe src={threeDModelUrl} ... /> */}
                      </div>
                    )}
                  </motion.div>
                )}
              </AnimatePresence>

              {/* Navigation Arrows */}
              {slides.length > 1 && (
                <>
                  <button 
                    onClick={() => changeSlide(-1)} 
                    className="absolute left-3 top-1/2 -translate-y-1/2 z-20 bg-black/40 hover:bg-black/60 p-2 rounded-full text-white transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-highlight/80 shadow-lg"
                    aria-label="Previous Slide"
                  >
                    <ChevronLeftIcon className="w-5 h-5 sm:w-6 sm:h-6" />
                  </button>
                  <button 
                    onClick={() => changeSlide(1)} 
                    className="absolute right-3 top-1/2 -translate-y-1/2 z-20 bg-black/40 hover:bg-black/60 p-2 rounded-full text-white transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-highlight/80 shadow-lg"
                    aria-label="Next Slide"
                  >
                    <ChevronRightIcon className="w-5 h-5 sm:w-6 sm:h-6" />
                  </button>
                </>
              )}

              {/* Dot Indicators */}
              {slides.length > 1 && (
                <div className="absolute bottom-4 left-1/2 -translate-x-1/2 z-20 flex space-x-2">
                  {slides.map((_, index) => (
                    <button 
                      key={index} 
                      onClick={() => setCurrentSlide(index)}
                      className={cn(
                        "w-2.5 h-2.5 sm:w-3 sm:h-3 rounded-full transition-all duration-300 focus:outline-none",
                        currentSlide === index ? "bg-highlight scale-125" : "bg-white/50 hover:bg-white/80"
                      )}
                      aria-label={`Go to slide ${index + 1}`}
                    />
                  ))}
                </div>
              )}
            </div>

            {/* Project Info */}
            <div className="flex flex-col justify-center pt-12 lg:pt-0">
              <div className="flex flex-wrap gap-2 mb-3">
                <span className={cn("px-3 py-1.5 rounded-md text-xs sm:text-sm font-medium shadow-md transition-colors duration-300", getTypeColor())}>
                  {type.charAt(0).toUpperCase() + type.slice(1)}
                </span>
                <span className={cn("px-3 py-1.5 rounded-md text-xs sm:text-sm font-medium shadow-md transition-colors duration-300", getStatusColor())}>
                  {status.charAt(0).toUpperCase() + status.slice(1)}
                </span>
              </div>
              <h1 className="text-3xl md:text-4xl lg:text-5xl font-display mb-3">{title}</h1>
              
              <div className="flex items-center mb-4 text-sm sm:text-base">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-4 w-4 sm:h-5 sm:w-5 text-highlight mr-2 flex-shrink-0"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={1.5}
                    d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                  />
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={1.5}
                    d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                  />
                </svg>
                <span className="text-foreground/70">{location}</span>
              </div>

              {reraNumber && (
                <div className="bg-muted/70 dark:bg-muted/30 px-3 py-1.5 rounded-md inline-flex items-center mb-4 text-xs sm:text-sm shadow-sm">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-4 w-4 sm:h-5 sm:w-5 text-highlight mr-2 flex-shrink-0"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={1.5}
                      d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"
                    />
                  </svg>
                  <span className="font-medium text-foreground/80">RERA:</span>&nbsp;<span className="text-foreground/70">{reraNumber}</span>
                </div>
              )}

              <p className="text-foreground/80 mb-6 text-sm sm:text-base leading-relaxed hyphens-auto">{description}</p>

              {/* Specifications */}
              {specifications && specifications.length > 0 && (
                <div className="mb-6">
                  <h3 className="text-xl md:text-2xl font-display mb-3">Specifications</h3>
                  <div className="bg-card/70 dark:bg-card/50 p-4 sm:p-6 rounded-lg shadow-md space-y-3">
                    {specifications.map((spec, index) => (
                      <div
                        key={index}
                        className={`flex justify-between items-start text-sm sm:text-base ${
                          index < specifications.length - 1
                            ? "pb-3 border-b border-border/60"
                            : ""
                        }`}
                      >
                        <span className="font-medium text-foreground/90">{spec.name}</span>
                        <span className="text-foreground/70 text-right ml-4">{spec.value}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Action Buttons */}
              {contactPhone && (
                <div className="mt-2">
                  <ShimmerButton
                    onClick={() => window.location.href = `tel:${contactPhone.replace(/\s+/g, "")}`}
                    shimmerColor="#FFFFFFCC"
                    shimmerSize="0.1em"
                    background="rgba(var(--highlight-rgb), 0.9)" // Using highlight color from theme
                    className="font-medium w-full sm:w-auto px-8 py-3 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-5 w-5 mr-2.5 flex-shrink-0"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"
                      />
                    </svg>
                    Contact Sales
                  </ShimmerButton>
                </div>
              )}
            </div>
          </div>
        </div>
      </section>

      {/* Enhanced Amenities Section */}
      {amenities && amenities.length > 0 && (
        <AmenitiesFeatures 
          amenities={amenities.map(amenity => ({
            icon: amenity.icon,
            title: amenity.name
          }))}
          projectName={title}
          brochureUrl={brochureUrl}
        />
      )}
    </>
  );
} 