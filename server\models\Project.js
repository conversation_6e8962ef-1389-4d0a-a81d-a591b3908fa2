const mongoose = require('mongoose');

const projectSchema = new mongoose.Schema({
  title: {
    type: String,
    required: [true, 'Project title is required'],
    trim: true,
    maxlength: [200, 'Title cannot exceed 200 characters']
  },
  description: {
    type: String,
    required: [true, 'Project description is required'],
    maxlength: [2000, 'Description cannot exceed 2000 characters']
  },
  location: {
    address: {
      type: String,
      required: [true, 'Address is required']
    },
    city: {
      type: String,
      required: [true, 'City is required']
    },
    state: {
      type: String,
      required: [true, 'State is required']
    },
    pincode: {
      type: String,
      required: [true, 'Pincode is required'],
      match: [/^\d{6}$/, 'Please enter a valid 6-digit pincode']
    },
    coordinates: {
      lat: {
        type: Number,
        min: [-90, 'Latitude must be between -90 and 90'],
        max: [90, 'Latitude must be between -90 and 90']
      },
      lng: {
        type: Number,
        min: [-180, 'Longitude must be between -180 and 180'],
        max: [180, 'Longitude must be between -180 and 180']
      }
    }
  },
  category: {
    type: String,
    enum: ['residential', 'commercial'],
    required: [true, 'Project category is required']
  },
  status: {
    type: String,
    enum: ['completed', 'ongoing', 'upcoming'],
    required: [true, 'Project status is required']
  },
  customTags: [{
    type: String,
    trim: true
  }],
  pricing: {
    startingPrice: {
      type: Number,
      min: [0, 'Starting price cannot be negative']
    },
    priceUnit: {
      type: String,
      enum: ['per_sqft', 'per_unit', 'lakh', 'crore'],
      default: 'lakh'
    },
    priceDetails: {
      type: String,
      maxlength: [500, 'Price details cannot exceed 500 characters']
    }
  },
  specifications: {
    totalUnits: {
      type: Number,
      min: [1, 'Total units must be at least 1']
    },
    projectArea: {
      type: String
    },
    towers: {
      type: Number,
      min: [1, 'Number of towers must be at least 1']
    },
    floors: {
      type: Number,
      min: [1, 'Number of floors must be at least 1']
    },
    parkingSpaces: {
      type: Number,
      min: [0, 'Parking spaces cannot be negative']
    }
  },
  coverImage: {
    url: {
      type: String,
      required: [true, 'Cover image URL is required']
    },
    alt: {
      type: String,
      required: [true, 'Cover image alt text is required']
    },
    filename: {
      type: String,
      required: [true, 'Cover image filename is required']
    }
  },
  models3D: [{
    url: {
      type: String,
      required: [true, '3D model URL is required']
    },
    title: {
      type: String,
      required: [true, '3D model title is required']
    },
    filename: {
      type: String,
      required: [true, '3D model filename is required']
    }
  }],
  brochure: {
    url: {
      type: String
    },
    filename: {
      type: String
    },
    originalName: {
      type: String
    },
    uploadDate: {
      type: Date,
      default: Date.now
    },
    isActive: {
      type: Boolean,
      default: true
    }
  },
  amenities: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Amenity'
  }],
  gallery: {
    categories: [{
      name: {
        type: String,
        required: [true, 'Gallery category name is required']
      },
      images: [{
        url: {
          type: String,
          required: [true, 'Image URL is required']
        },
        alt: {
          type: String,
          required: [true, 'Image alt text is required']
        },
        filename: {
          type: String,
          required: [true, 'Image filename is required']
        }
      }]
    }]
  },
  rera: {
    registrationNumber: {
      type: String,
      trim: true
    },
    authority: {
      type: String,
      trim: true
    },
    state: {
      type: String,
      trim: true
    },
    validityDate: {
      type: Date
    },
    approvalDate: {
      type: Date
    },
    completionTimeline: {
      type: Date
    },
    certificate: {
      url: {
        type: String
      },
      filename: {
        type: String
      }
    },
    qrCode: {
      url: {
        type: String
      },
      filename: {
        type: String
      }
    },
    documents: [{
      type: {
        type: String,
        enum: ['NOC', 'Layout', 'Environmental', 'Other']
      },
      url: {
        type: String,
        required: [true, 'Document URL is required']
      },
      filename: {
        type: String,
        required: [true, 'Document filename is required']
      },
      uploadDate: {
        type: Date,
        default: Date.now
      }
    }]
  },
  seo: {
    metaTitle: {
      type: String,
      maxlength: [60, 'Meta title cannot exceed 60 characters']
    },
    metaDescription: {
      type: String,
      maxlength: [160, 'Meta description cannot exceed 160 characters']
    },
    keywords: [{
      type: String,
      trim: true
    }],
    canonicalUrl: {
      type: String
    }
  },
  isActive: {
    type: Boolean,
    default: true
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Created by user is required']
  }
}, {
  timestamps: true
});

// Indexes for performance
projectSchema.index({ title: 1 });
projectSchema.index({ category: 1 });
projectSchema.index({ status: 1 });
projectSchema.index({ isActive: 1 });
projectSchema.index({ 'location.city': 1 });
projectSchema.index({ 'location.state': 1 });
projectSchema.index({ createdBy: 1 });
projectSchema.index({ title: 'text', description: 'text' });

// Virtual for full location
projectSchema.virtual('fullLocation').get(function() {
  return `${this.location.address}, ${this.location.city}, ${this.location.state} - ${this.location.pincode}`;
});

// Static method to get projects by category and status
projectSchema.statics.getByFilters = function(filters = {}) {
  const query = { isActive: true };

  if (filters.category) query.category = filters.category;
  if (filters.status) query.status = filters.status;
  if (filters.city) query['location.city'] = new RegExp(filters.city, 'i');
  if (filters.search) {
    query.$text = { $search: filters.search };
  }

  return this.find(query)
    .populate('amenities', 'name icon category')
    .populate('createdBy', 'firstName lastName')
    .sort({ createdAt: -1 });
};

// Method to generate SEO-friendly slug
projectSchema.methods.generateSlug = function() {
  return this.title
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/(^-|-$)/g, '');
};

module.exports = mongoose.model('Project', projectSchema);
