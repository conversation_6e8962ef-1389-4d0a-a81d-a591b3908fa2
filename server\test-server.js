const express = require('express');
const cors = require('cors');
require('dotenv').config();

const connectDB = require('./config/database');

const app = express();
const PORT = process.env.PORT || 5000;

// Connect to MongoDB
connectDB();

// Basic middleware
app.use(cors());
app.use(express.json());

// Test route
app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'OK', 
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development'
  });
});

// Test projects route
app.get('/api/projects', async (req, res) => {
  try {
    const Project = require('./models/Project');
    const projects = await Project.find({ isActive: true })
      .populate('amenities', 'name icon category')
      .populate('createdBy', 'firstName lastName')
      .select('-__v')
      .sort({ createdAt: -1 })
      .limit(10);

    // Transform projects for frontend
    const transformedProjects = projects.map(project => ({
      id: project._id,
      title: project.title,
      description: project.description,
      location: `${project.location.city}, ${project.location.state}`,
      fullLocation: project.fullLocation,
      category: project.category,
      status: project.status,
      imageSrc: project.coverImage.url,
      href: `/projects/${project.generateSlug()}`,
      pricing: project.pricing,
      specifications: project.specifications,
      amenities: project.amenities,
      customTags: project.customTags,
      createdAt: project.createdAt
    }));

    res.json({
      success: true,
      data: {
        projects: transformedProjects,
        pagination: {
          currentPage: 1,
          totalPages: 1,
          totalProjects: transformedProjects.length,
          hasNextPage: false,
          hasPrevPage: false
        }
      }
    });
  } catch (error) {
    console.error('Get projects error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error fetching projects'
    });
  }
});

// Test testimonials route
app.get('/api/testimonials', async (req, res) => {
  try {
    // For now, return empty array since we don't have testimonials seeded
    res.json({
      success: true,
      data: {
        testimonials: []
      }
    });
  } catch (error) {
    console.error('Get testimonials error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error fetching testimonials'
    });
  }
});

// Test amenities route
app.get('/api/amenities', async (req, res) => {
  try {
    const Amenity = require('./models/Amenity');
    const amenities = await Amenity.find({ isActive: true })
      .select('-__v')
      .sort({ name: 1 });

    res.json({
      success: true,
      data: {
        amenities
      }
    });
  } catch (error) {
    console.error('Get amenities error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error fetching amenities'
    });
  }
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    success: false,
    message: 'Internal Server Error'
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'Route not found'
  });
});

app.listen(PORT, () => {
  console.log(`🚀 Test Server running on port ${PORT}`);
  console.log(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log(`🔗 API Base URL: http://localhost:${PORT}/api`);
});

module.exports = app;
