const express = require('express');
const { body, validationResult, query } = require('express-validator');
const Project = require('../models/Project');
const { authenticateToken, requireAdmin, optionalAuth } = require('../middleware/auth');
const { uploadConfigs, optimizeImage, generateFileUrl } = require('../middleware/upload');

const router = express.Router();

// @route   GET /api/projects
// @desc    Get all projects with filtering
// @access  Public
router.get('/', [
  query('category').optional().isIn(['residential', 'commercial']),
  query('status').optional().isIn(['completed', 'ongoing', 'upcoming']),
  query('city').optional().isString(),
  query('search').optional().isString(),
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 50 })
], async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Invalid query parameters',
        errors: errors.array()
      });
    }

    const {
      category,
      status,
      city,
      search,
      page = 1,
      limit = 10
    } = req.query;

    // Build filter object
    const filters = { isActive: true };
    if (category) filters.category = category;
    if (status) filters.status = status;
    if (city) filters['location.city'] = new RegExp(city, 'i');
    if (search) {
      filters.$or = [
        { title: new RegExp(search, 'i') },
        { description: new RegExp(search, 'i') },
        { 'location.city': new RegExp(search, 'i') },
        { 'location.address': new RegExp(search, 'i') }
      ];
    }

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Get projects with pagination
    const projects = await Project.find(filters)
      .populate('amenities', 'name icon category')
      .populate('createdBy', 'firstName lastName')
      .select('-__v')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit));

    // Get total count for pagination
    const totalProjects = await Project.countDocuments(filters);
    const totalPages = Math.ceil(totalProjects / limit);

    // Transform projects for frontend
    const transformedProjects = projects.map(project => ({
      id: project._id,
      title: project.title,
      description: project.description,
      location: `${project.location.city}, ${project.location.state}`,
      fullLocation: project.fullLocation,
      category: project.category,
      status: project.status,
      imageSrc: project.coverImage.url,
      href: `/projects/${project.generateSlug()}`,
      pricing: project.pricing,
      specifications: project.specifications,
      amenities: project.amenities,
      customTags: project.customTags,
      createdAt: project.createdAt
    }));

    res.json({
      success: true,
      data: {
        projects: transformedProjects,
        pagination: {
          currentPage: parseInt(page),
          totalPages,
          totalProjects,
          hasNextPage: page < totalPages,
          hasPrevPage: page > 1
        }
      }
    });

  } catch (error) {
    console.error('Get projects error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error fetching projects'
    });
  }
});

// @route   GET /api/projects/:id
// @desc    Get single project by ID
// @access  Public
router.get('/:id', async (req, res) => {
  try {
    const project = await Project.findOne({
      _id: req.params.id,
      isActive: true
    })
    .populate('amenities', 'name icon category description')
    .populate('createdBy', 'firstName lastName');

    if (!project) {
      return res.status(404).json({
        success: false,
        message: 'Project not found'
      });
    }

    // Transform project for frontend
    const transformedProject = {
      id: project._id,
      title: project.title,
      description: project.description,
      location: `${project.location.city}, ${project.location.state}`,
      fullLocation: project.fullLocation,
      category: project.category,
      status: project.status,
      imageSrc: project.coverImage.url,
      coverImage: project.coverImage,
      pricing: project.pricing,
      specifications: project.specifications,
      amenities: project.amenities,
      gallery: project.gallery,
      models3D: project.models3D,
      brochure: project.brochure,
      rera: project.rera,
      customTags: project.customTags,
      seo: project.seo,
      createdAt: project.createdAt,
      slug: project.generateSlug()
    };

    res.json({
      success: true,
      data: {
        project: transformedProject
      }
    });

  } catch (error) {
    console.error('Get project error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error fetching project'
    });
  }
});

// @route   POST /api/projects
// @desc    Create new project
// @access  Private (Admin)
router.post('/', [
  authenticateToken,
  requireAdmin,
  uploadConfigs.projectImages,
  optimizeImage,
  body('title').notEmpty().withMessage('Title is required'),
  body('description').notEmpty().withMessage('Description is required'),
  body('category').isIn(['residential', 'commercial']).withMessage('Invalid category'),
  body('status').isIn(['completed', 'ongoing', 'upcoming']).withMessage('Invalid status'),
  body('location.address').notEmpty().withMessage('Address is required'),
  body('location.city').notEmpty().withMessage('City is required'),
  body('location.state').notEmpty().withMessage('State is required'),
  body('location.pincode').matches(/^\d{6}$/).withMessage('Invalid pincode')
], async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    // Check if cover image is uploaded
    if (!req.files || !req.files.coverImage) {
      return res.status(400).json({
        success: false,
        message: 'Cover image is required'
      });
    }

    const projectData = {
      ...req.body,
      createdBy: req.user._id,
      coverImage: {
        url: generateFileUrl(req, req.files.coverImage[0].filename, 'projects/'),
        alt: req.body.coverImageAlt || req.body.title,
        filename: req.files.coverImage[0].filename
      }
    };

    // Handle gallery images if uploaded
    if (req.files.galleryImages) {
      projectData.gallery = {
        categories: [{
          name: 'General',
          images: req.files.galleryImages.map(file => ({
            url: generateFileUrl(req, file.filename, 'projects/'),
            alt: req.body.title,
            filename: file.filename
          }))
        }]
      };
    }

    // Handle brochure if uploaded
    if (req.files.brochure) {
      projectData.brochure = {
        url: generateFileUrl(req, req.files.brochure[0].filename, 'brochures/'),
        filename: req.files.brochure[0].filename,
        originalName: req.files.brochure[0].originalname,
        uploadDate: new Date(),
        isActive: true
      };
    }

    const project = new Project(projectData);
    await project.save();

    res.status(201).json({
      success: true,
      message: 'Project created successfully',
      data: {
        project: {
          id: project._id,
          title: project.title,
          slug: project.generateSlug()
        }
      }
    });

  } catch (error) {
    console.error('Create project error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error creating project'
    });
  }
});

module.exports = router;
