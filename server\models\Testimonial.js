const mongoose = require('mongoose');

const testimonialSchema = new mongoose.Schema({
  type: {
    type: String,
    enum: ['text', 'video'],
    required: [true, 'Testimonial type is required']
  },
  clientName: {
    type: String,
    required: [true, 'Client name is required'],
    trim: true,
    maxlength: [100, 'Client name cannot exceed 100 characters']
  },
  clientDesignation: {
    type: String,
    trim: true,
    maxlength: [100, 'Client designation cannot exceed 100 characters']
  },
  clientPhoto: {
    url: {
      type: String
    },
    filename: {
      type: String
    }
  },
  content: {
    type: String,
    required: function() {
      return this.type === 'text';
    },
    maxlength: [1000, 'Content cannot exceed 1000 characters']
  },
  videoUrl: {
    type: String,
    required: function() {
      return this.type === 'video';
    }
  },
  videoThumbnail: {
    url: {
      type: String,
      required: function() {
        return this.type === 'video';
      }
    },
    filename: {
      type: String,
      required: function() {
        return this.type === 'video';
      }
    }
  },
  rating: {
    type: Number,
    min: [1, 'Rating must be at least 1'],
    max: [5, 'Rating cannot exceed 5'],
    default: 5
  },
  projectReference: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Project'
  },
  isActive: {
    type: Boolean,
    default: true
  },
  displayOrder: {
    type: Number,
    default: 0
  }
}, {
  timestamps: true
});

// Indexes for performance
testimonialSchema.index({ type: 1 });
testimonialSchema.index({ isActive: 1 });
testimonialSchema.index({ displayOrder: 1 });
testimonialSchema.index({ projectReference: 1 });
testimonialSchema.index({ rating: -1 });

// Static method to get active testimonials
testimonialSchema.statics.getActive = function(type = null) {
  const query = { isActive: true };
  if (type) query.type = type;
  
  return this.find(query)
    .populate('projectReference', 'title category')
    .sort({ displayOrder: 1, createdAt: -1 });
};

// Static method to get testimonials by project
testimonialSchema.statics.getByProject = function(projectId) {
  return this.find({ 
    projectReference: projectId, 
    isActive: true 
  })
  .sort({ displayOrder: 1, createdAt: -1 });
};

// Method to update display order
testimonialSchema.statics.updateDisplayOrder = async function(testimonialIds) {
  const bulkOps = testimonialIds.map((id, index) => ({
    updateOne: {
      filter: { _id: id },
      update: { displayOrder: index + 1 }
    }
  }));
  
  return await this.bulkWrite(bulkOps);
};

module.exports = mongoose.model('Testimonial', testimonialSchema);
