const express = require('express');
const { uploadConfigs, optimizeImage, generateFileUrl } = require('../middleware/upload');
const { authenticateToken, requireAdmin } = require('../middleware/auth');

const router = express.Router();

// @route   POST /api/upload/single
// @desc    Upload single file
// @access  Private (Admin)
router.post('/single', [
  authenticateToken,
  requireAdmin,
  uploadConfigs.single('file'),
  optimizeImage
], async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'No file uploaded'
      });
    }

    const fileUrl = generateFileUrl(req, req.file.filename, 'misc/');

    res.json({
      success: true,
      message: 'File uploaded successfully',
      data: {
        file: {
          url: fileUrl,
          filename: req.file.filename,
          originalName: req.file.originalname,
          mimetype: req.file.mimetype,
          size: req.file.size
        }
      }
    });

  } catch (error) {
    console.error('File upload error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error uploading file'
    });
  }
});

// @route   POST /api/upload/multiple
// @desc    Upload multiple files
// @access  Private (Admin)
router.post('/multiple', [
  authenticateToken,
  requireAdmin,
  uploadConfigs.multiple('files', 10),
  optimizeImage
], async (req, res) => {
  try {
    if (!req.files || req.files.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'No files uploaded'
      });
    }

    const uploadedFiles = req.files.map(file => ({
      url: generateFileUrl(req, file.filename, 'misc/'),
      filename: file.filename,
      originalName: file.originalname,
      mimetype: file.mimetype,
      size: file.size
    }));

    res.json({
      success: true,
      message: 'Files uploaded successfully',
      data: {
        files: uploadedFiles
      }
    });

  } catch (error) {
    console.error('Multiple file upload error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error uploading files'
    });
  }
});

module.exports = router;
