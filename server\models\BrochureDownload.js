const mongoose = require('mongoose');

const brochureDownloadSchema = new mongoose.Schema({
  projectId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Project',
    required: [true, 'Project ID is required']
  },
  userDetails: {
    fullName: {
      type: String,
      required: [true, 'Full name is required'],
      trim: true,
      maxlength: [100, 'Full name cannot exceed 100 characters']
    },
    email: {
      type: String,
      required: [true, 'Email is required'],
      lowercase: true,
      trim: true,
      match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email']
    },
    phone: {
      type: String,
      required: [true, 'Phone number is required'],
      trim: true,
      match: [/^[6-9]\d{9}$/, 'Please enter a valid 10-digit phone number']
    },
    location: {
      type: String,
      trim: true,
      maxlength: [100, 'Location cannot exceed 100 characters']
    },
    interestedIn: {
      type: String,
      trim: true,
      maxlength: [100, 'Interest details cannot exceed 100 characters']
    },
    budget: {
      type: String,
      trim: true,
      maxlength: [50, 'Budget cannot exceed 50 characters']
    },
    message: {
      type: String,
      trim: true,
      maxlength: [500, 'Message cannot exceed 500 characters']
    },
    source: {
      type: String,
      enum: ['website', 'social_media', 'referral', 'advertisement', 'other'],
      default: 'website'
    }
  },
  brochureUrl: {
    type: String,
    required: [true, 'Brochure URL is required']
  },
  downloadedAt: {
    type: Date,
    default: Date.now
  },
  ipAddress: {
    type: String
  },
  userAgent: {
    type: String
  },
  isFollowedUp: {
    type: Boolean,
    default: false
  },
  followUpNotes: {
    type: String,
    maxlength: [1000, 'Follow-up notes cannot exceed 1000 characters']
  },
  leadStatus: {
    type: String,
    enum: ['new', 'contacted', 'interested', 'not_interested', 'converted'],
    default: 'new'
  },
  assignedTo: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true
});

// Indexes for performance
brochureDownloadSchema.index({ projectId: 1 });
brochureDownloadSchema.index({ 'userDetails.email': 1 });
brochureDownloadSchema.index({ 'userDetails.phone': 1 });
brochureDownloadSchema.index({ leadStatus: 1 });
brochureDownloadSchema.index({ isFollowedUp: 1 });
brochureDownloadSchema.index({ assignedTo: 1 });
brochureDownloadSchema.index({ downloadedAt: -1 });

// Compound indexes
brochureDownloadSchema.index({ projectId: 1, leadStatus: 1 });
brochureDownloadSchema.index({ assignedTo: 1, leadStatus: 1 });

// Static method to get leads by status
brochureDownloadSchema.statics.getByStatus = function(status) {
  return this.find({ leadStatus: status })
    .populate('projectId', 'title category location')
    .populate('assignedTo', 'firstName lastName email')
    .sort({ downloadedAt: -1 });
};

// Static method to get leads requiring follow-up
brochureDownloadSchema.statics.getFollowUpRequired = function() {
  return this.find({ 
    isFollowedUp: false,
    leadStatus: { $in: ['new', 'contacted'] }
  })
  .populate('projectId', 'title category location')
  .populate('assignedTo', 'firstName lastName email')
  .sort({ downloadedAt: -1 });
};

// Static method to get lead statistics
brochureDownloadSchema.statics.getStatistics = async function(dateRange = {}) {
  const matchStage = {};
  
  if (dateRange.start && dateRange.end) {
    matchStage.downloadedAt = {
      $gte: new Date(dateRange.start),
      $lte: new Date(dateRange.end)
    };
  }

  return await this.aggregate([
    { $match: matchStage },
    {
      $group: {
        _id: null,
        totalLeads: { $sum: 1 },
        newLeads: {
          $sum: { $cond: [{ $eq: ['$leadStatus', 'new'] }, 1, 0] }
        },
        contactedLeads: {
          $sum: { $cond: [{ $eq: ['$leadStatus', 'contacted'] }, 1, 0] }
        },
        interestedLeads: {
          $sum: { $cond: [{ $eq: ['$leadStatus', 'interested'] }, 1, 0] }
        },
        convertedLeads: {
          $sum: { $cond: [{ $eq: ['$leadStatus', 'converted'] }, 1, 0] }
        },
        followUpRequired: {
          $sum: { $cond: [{ $eq: ['$isFollowedUp', false] }, 1, 0] }
        }
      }
    }
  ]);
};

// Method to check for duplicate leads
brochureDownloadSchema.statics.checkDuplicate = function(email, phone, projectId) {
  return this.findOne({
    $or: [
      { 'userDetails.email': email },
      { 'userDetails.phone': phone }
    ],
    projectId: projectId
  });
};

module.exports = mongoose.model('BrochureDownload', brochureDownloadSchema);
