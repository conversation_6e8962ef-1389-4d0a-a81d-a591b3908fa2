const express = require('express');
const { body, validationResult, query } = require('express-validator');
const BrochureDownload = require('../models/BrochureDownload');
const Project = require('../models/Project');
const { authenticateToken, requireAdmin } = require('../middleware/auth');

const router = express.Router();

// @route   POST /api/leads/brochure-download/:projectId
// @desc    Handle brochure download request and create lead
// @access  Public
router.post('/brochure-download/:projectId', [
  body('fullName').notEmpty().withMessage('Full name is required'),
  body('email').isEmail().normalizeEmail().withMessage('Valid email is required'),
  body('phone').matches(/^[6-9]\d{9}$/).withMessage('Valid 10-digit phone number is required'),
  body('location').optional().isLength({ max: 100 }).withMessage('Location cannot exceed 100 characters'),
  body('interestedIn').optional().isLength({ max: 100 }).withMessage('Interest details cannot exceed 100 characters'),
  body('budget').optional().isLength({ max: 50 }).withMessage('Budget cannot exceed 50 characters'),
  body('message').optional().isLength({ max: 500 }).withMessage('Message cannot exceed 500 characters'),
  body('source').optional().isIn(['website', 'social_media', 'referral', 'advertisement', 'other']).withMessage('Invalid source')
], async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { projectId } = req.params;
    const { fullName, email, phone, location, interestedIn, budget, message, source } = req.body;

    // Check if project exists and has brochure
    const project = await Project.findOne({ _id: projectId, isActive: true });
    if (!project) {
      return res.status(404).json({
        success: false,
        message: 'Project not found'
      });
    }

    if (!project.brochure || !project.brochure.url) {
      return res.status(400).json({
        success: false,
        message: 'Brochure not available for this project'
      });
    }

    // Check for duplicate lead (same email or phone for same project)
    const existingLead = await BrochureDownload.checkDuplicate(email, phone, projectId);
    if (existingLead) {
      // Return existing brochure URL but don't create duplicate lead
      return res.json({
        success: true,
        message: 'Brochure download link sent',
        data: {
          brochureUrl: project.brochure.url,
          isExistingLead: true
        }
      });
    }

    // Get client IP and user agent
    const ipAddress = req.ip || req.connection.remoteAddress;
    const userAgent = req.get('User-Agent');

    // Create new lead
    const leadData = {
      projectId,
      userDetails: {
        fullName,
        email,
        phone,
        location,
        interestedIn,
        budget,
        message,
        source: source || 'website'
      },
      brochureUrl: project.brochure.url,
      ipAddress,
      userAgent
    };

    const lead = new BrochureDownload(leadData);
    await lead.save();

    // Populate project details for response
    await lead.populate('projectId', 'title category location');

    res.status(201).json({
      success: true,
      message: 'Lead created successfully. Brochure download link provided.',
      data: {
        brochureUrl: project.brochure.url,
        leadId: lead._id,
        isExistingLead: false
      }
    });

  } catch (error) {
    console.error('Brochure download error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error processing brochure download'
    });
  }
});

// @route   GET /api/leads
// @desc    Get all leads with filtering
// @access  Private (Admin)
router.get('/', [
  authenticateToken,
  requireAdmin,
  query('status').optional().isIn(['new', 'contacted', 'interested', 'not_interested', 'converted']),
  query('project').optional().isMongoId(),
  query('assignedTo').optional().isMongoId(),
  query('followUp').optional().isBoolean(),
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 100 }),
  query('startDate').optional().isISO8601(),
  query('endDate').optional().isISO8601()
], async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Invalid query parameters',
        errors: errors.array()
      });
    }

    const {
      status,
      project,
      assignedTo,
      followUp,
      page = 1,
      limit = 20,
      startDate,
      endDate
    } = req.query;

    // Build filter object
    const filters = {};
    if (status) filters.leadStatus = status;
    if (project) filters.projectId = project;
    if (assignedTo) filters.assignedTo = assignedTo;
    if (followUp !== undefined) filters.isFollowedUp = followUp === 'true';

    // Date range filter
    if (startDate || endDate) {
      filters.downloadedAt = {};
      if (startDate) filters.downloadedAt.$gte = new Date(startDate);
      if (endDate) filters.downloadedAt.$lte = new Date(endDate);
    }

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Get leads with pagination
    const leads = await BrochureDownload.find(filters)
      .populate('projectId', 'title category location')
      .populate('assignedTo', 'firstName lastName email')
      .select('-__v')
      .sort({ downloadedAt: -1 })
      .skip(skip)
      .limit(parseInt(limit));

    // Get total count for pagination
    const totalLeads = await BrochureDownload.countDocuments(filters);
    const totalPages = Math.ceil(totalLeads / limit);

    res.json({
      success: true,
      data: {
        leads,
        pagination: {
          currentPage: parseInt(page),
          totalPages,
          totalLeads,
          hasNextPage: page < totalPages,
          hasPrevPage: page > 1
        }
      }
    });

  } catch (error) {
    console.error('Get leads error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error fetching leads'
    });
  }
});

// @route   GET /api/leads/statistics
// @desc    Get lead statistics
// @access  Private (Admin)
router.get('/statistics', [
  authenticateToken,
  requireAdmin,
  query('startDate').optional().isISO8601(),
  query('endDate').optional().isISO8601()
], async (req, res) => {
  try {
    const { startDate, endDate } = req.query;
    
    const dateRange = {};
    if (startDate) dateRange.start = startDate;
    if (endDate) dateRange.end = endDate;

    const statistics = await BrochureDownload.getStatistics(dateRange);

    res.json({
      success: true,
      data: {
        statistics: statistics[0] || {
          totalLeads: 0,
          newLeads: 0,
          contactedLeads: 0,
          interestedLeads: 0,
          convertedLeads: 0,
          followUpRequired: 0
        }
      }
    });

  } catch (error) {
    console.error('Get lead statistics error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error fetching lead statistics'
    });
  }
});

// @route   GET /api/leads/:id
// @desc    Get single lead by ID
// @access  Private (Admin)
router.get('/:id', [
  authenticateToken,
  requireAdmin
], async (req, res) => {
  try {
    const lead = await BrochureDownload.findById(req.params.id)
      .populate('projectId', 'title category location')
      .populate('assignedTo', 'firstName lastName email');

    if (!lead) {
      return res.status(404).json({
        success: false,
        message: 'Lead not found'
      });
    }

    res.json({
      success: true,
      data: {
        lead
      }
    });

  } catch (error) {
    console.error('Get lead error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error fetching lead'
    });
  }
});

// @route   PUT /api/leads/:id
// @desc    Update lead status and notes
// @access  Private (Admin)
router.put('/:id', [
  authenticateToken,
  requireAdmin,
  body('leadStatus').optional().isIn(['new', 'contacted', 'interested', 'not_interested', 'converted']).withMessage('Invalid lead status'),
  body('followUpNotes').optional().isLength({ max: 1000 }).withMessage('Follow-up notes cannot exceed 1000 characters'),
  body('isFollowedUp').optional().isBoolean().withMessage('isFollowedUp must be a boolean'),
  body('assignedTo').optional().isMongoId().withMessage('Invalid assigned user ID')
], async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const lead = await BrochureDownload.findById(req.params.id);
    if (!lead) {
      return res.status(404).json({
        success: false,
        message: 'Lead not found'
      });
    }

    const updateData = { ...req.body };

    const updatedLead = await BrochureDownload.findByIdAndUpdate(
      req.params.id,
      updateData,
      { new: true, runValidators: true }
    )
    .populate('projectId', 'title category location')
    .populate('assignedTo', 'firstName lastName email');

    res.json({
      success: true,
      message: 'Lead updated successfully',
      data: {
        lead: updatedLead
      }
    });

  } catch (error) {
    console.error('Update lead error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error updating lead'
    });
  }
});

module.exports = router;
