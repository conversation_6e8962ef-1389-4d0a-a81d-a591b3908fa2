// API configuration and utilities
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';

// API response interface
interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  errors?: any[];
}

// API error class
class ApiError extends Error {
  status: number;
  errors?: any[];

  constructor(message: string, status: number, errors?: any[]) {
    super(message);
    this.name = 'ApiError';
    this.status = status;
    this.errors = errors;
  }
}

// Generic API request function
async function apiRequest<T = any>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> {
  const url = `${API_BASE_URL}${endpoint}`;
  
  const defaultHeaders: HeadersInit = {
    'Content-Type': 'application/json',
  };

  // Get auth token from localStorage if available
  if (typeof window !== 'undefined') {
    const token = localStorage.getItem('authToken');
    if (token) {
      defaultHeaders.Authorization = `Bearer ${token}`;
    }
  }

  const config: RequestInit = {
    headers: {
      ...defaultHeaders,
      ...options.headers,
    },
    ...options,
  };

  try {
    const response = await fetch(url, config);
    const data: ApiResponse<T> = await response.json();

    if (!response.ok) {
      throw new ApiError(
        data.message || 'An error occurred',
        response.status,
        data.errors
      );
    }

    return data.data as T;
  } catch (error) {
    if (error instanceof ApiError) {
      throw error;
    }
    
    // Network or other errors
    throw new ApiError(
      'Network error or server unavailable',
      0
    );
  }
}

// Project API functions
export const projectsApi = {
  // Get all projects with filtering
  getAll: async (params?: {
    category?: 'residential' | 'commercial';
    status?: 'completed' | 'ongoing' | 'upcoming';
    city?: string;
    search?: string;
    page?: number;
    limit?: number;
  }) => {
    const searchParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          searchParams.append(key, value.toString());
        }
      });
    }
    
    const endpoint = `/projects${searchParams.toString() ? `?${searchParams.toString()}` : ''}`;
    return apiRequest<{
      projects: any[];
      pagination: {
        currentPage: number;
        totalPages: number;
        totalProjects: number;
        hasNextPage: boolean;
        hasPrevPage: boolean;
      };
    }>(endpoint);
  },

  // Get single project by ID
  getById: async (id: string) => {
    return apiRequest<{ project: any }>(`/projects/${id}`);
  },

  // Create new project (admin only)
  create: async (projectData: FormData) => {
    return apiRequest<{ project: any }>('/projects', {
      method: 'POST',
      body: projectData,
      headers: {}, // Remove Content-Type to let browser set it for FormData
    });
  },
};

// Amenities API functions
export const amenitiesApi = {
  // Get all amenities
  getAll: async (params?: {
    category?: string;
    active?: boolean;
  }) => {
    const searchParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          searchParams.append(key, value.toString());
        }
      });
    }
    
    const endpoint = `/amenities${searchParams.toString() ? `?${searchParams.toString()}` : ''}`;
    return apiRequest<{ amenities: any[] }>(endpoint);
  },

  // Get amenities by category
  getByCategory: async () => {
    return apiRequest<{ categories: any[] }>('/amenities/categories');
  },
};

// Testimonials API functions
export const testimonialsApi = {
  // Get all testimonials
  getAll: async (params?: {
    type?: 'text' | 'video';
    active?: boolean;
    project?: string;
  }) => {
    const searchParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          searchParams.append(key, value.toString());
        }
      });
    }
    
    const endpoint = `/testimonials${searchParams.toString() ? `?${searchParams.toString()}` : ''}`;
    return apiRequest<{ testimonials: any[] }>(endpoint);
  },
};

// Leads API functions
export const leadsApi = {
  // Submit brochure download request
  submitBrochureDownload: async (projectId: string, formData: {
    fullName: string;
    email: string;
    phone: string;
    location?: string;
    interestedIn?: string;
    budget?: string;
    message?: string;
    source?: string;
  }) => {
    return apiRequest<{
      brochureUrl: string;
      leadId?: string;
      isExistingLead: boolean;
    }>(`/leads/brochure-download/${projectId}`, {
      method: 'POST',
      body: JSON.stringify(formData),
    });
  },

  // Get all leads (admin only)
  getAll: async (params?: {
    status?: string;
    project?: string;
    assignedTo?: string;
    followUp?: boolean;
    page?: number;
    limit?: number;
    startDate?: string;
    endDate?: string;
  }) => {
    const searchParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          searchParams.append(key, value.toString());
        }
      });
    }
    
    const endpoint = `/leads${searchParams.toString() ? `?${searchParams.toString()}` : ''}`;
    return apiRequest<{
      leads: any[];
      pagination: any;
    }>(endpoint);
  },

  // Get lead statistics (admin only)
  getStatistics: async (params?: {
    startDate?: string;
    endDate?: string;
  }) => {
    const searchParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          searchParams.append(key, value.toString());
        }
      });
    }
    
    const endpoint = `/leads/statistics${searchParams.toString() ? `?${searchParams.toString()}` : ''}`;
    return apiRequest<{ statistics: any }>(endpoint);
  },
};

// Auth API functions
export const authApi = {
  // Login
  login: async (credentials: { email: string; password: string }) => {
    return apiRequest<{
      token: string;
      user: any;
    }>('/auth/login', {
      method: 'POST',
      body: JSON.stringify(credentials),
    });
  },

  // Get current user profile
  getProfile: async () => {
    return apiRequest<{ user: any }>('/auth/profile');
  },

  // Change password
  changePassword: async (passwords: {
    currentPassword: string;
    newPassword: string;
    confirmPassword: string;
  }) => {
    return apiRequest('/auth/change-password', {
      method: 'PUT',
      body: JSON.stringify(passwords),
    });
  },

  // Logout
  logout: async () => {
    return apiRequest('/auth/logout', {
      method: 'POST',
    });
  },

  // Refresh token
  refreshToken: async () => {
    return apiRequest<{ token: string }>('/auth/refresh-token', {
      method: 'POST',
    });
  },
};

// Export the ApiError class for error handling
export { ApiError };

// Health check function
export const healthCheck = async () => {
  return apiRequest<{
    status: string;
    timestamp: string;
    environment: string;
  }>('/health');
};
