const express = require('express');
const cors = require('cors');
require('dotenv').config();

const connectDB = require('./config/database');

// Load models
const Project = require('./models/Project');
const Amenity = require('./models/Amenity');
const User = require('./models/User');
const Testimonial = require('./models/Testimonial');

const app = express();
const PORT = process.env.PORT || 5000;

// Connect to MongoDB
connectDB();

// Basic middleware
app.use(cors());
app.use(express.json());

// Test route
app.get('/api/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development'
  });
});

// Test projects route
app.get('/api/projects', async (req, res) => {
  try {
    const projects = await Project.find({ isActive: true })
      .populate('amenities', 'name icon category')
      .populate('createdBy', 'firstName lastName')
      .select('-__v')
      .sort({ createdAt: -1 })
      .limit(10);

    // Transform projects for frontend
    const transformedProjects = projects.map(project => ({
      id: project._id,
      title: project.title,
      description: project.description,
      location: `${project.location.city}, ${project.location.state}`,
      fullLocation: project.fullLocation,
      category: project.category,
      status: project.status,
      imageSrc: project.coverImage.url,
      href: `/projects/${project.generateSlug()}`,
      pricing: project.pricing,
      specifications: project.specifications,
      amenities: project.amenities,
      customTags: project.customTags,
      createdAt: project.createdAt
    }));

    res.json({
      success: true,
      data: {
        projects: transformedProjects,
        pagination: {
          currentPage: 1,
          totalPages: 1,
          totalProjects: transformedProjects.length,
          hasNextPage: false,
          hasPrevPage: false
        }
      }
    });
  } catch (error) {
    console.error('Get projects error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error fetching projects'
    });
  }
});

// Test amenities route
app.get('/api/amenities', async (req, res) => {
  try {
    const amenities = await Amenity.find({ isActive: true })
      .select('-__v')
      .sort({ name: 1 });

    res.json({
      success: true,
      data: {
        amenities
      }
    });
  } catch (error) {
    console.error('Get amenities error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error fetching amenities'
    });
  }
});

// Test testimonials route
app.get('/api/testimonials', async (req, res) => {
  try {
    const testimonials = await Testimonial.find({ isActive: true })
      .populate('projectReference', 'title category location')
      .select('-__v')
      .sort({ displayOrder: 1, createdAt: -1 });

    // Transform testimonials for frontend compatibility
    const transformedTestimonials = testimonials.map(testimonial => ({
      id: testimonial._id,
      name: testimonial.clientName,
      role: testimonial.clientDesignation,
      quote: testimonial.content,
      avatarSrc: testimonial.clientPhoto?.url,
      type: testimonial.type,
      videoSrc: testimonial.videoUrl,
      thumbnailSrc: testimonial.videoThumbnail?.url,
      rating: testimonial.rating,
      project: testimonial.projectReference,
      displayOrder: testimonial.displayOrder,
      createdAt: testimonial.createdAt
    }));

    res.json({
      success: true,
      data: {
        testimonials: transformedTestimonials
      }
    });
  } catch (error) {
    console.error('Get testimonials error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error fetching testimonials'
    });
  }
});

// Simple brochure download route
app.post('/api/leads/brochure-download/:projectId', async (req, res) => {
  try {
    const { projectId } = req.params;
    const { fullName, email, phone } = req.body;

    // Basic validation
    if (!fullName || !email || !phone) {
      return res.status(400).json({
        success: false,
        message: 'Full name, email, and phone are required'
      });
    }

    const project = await Project.findOne({ _id: projectId, isActive: true });

    if (!project) {
      return res.status(404).json({
        success: false,
        message: 'Project not found'
      });
    }

    // For now, return a dummy brochure URL
    const brochureUrl = 'https://example.com/brochure.pdf';

    res.status(201).json({
      success: true,
      message: 'Lead created successfully. Brochure download link provided.',
      data: {
        brochureUrl,
        isExistingLead: false
      }
    });

  } catch (error) {
    console.error('Brochure download error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error processing brochure download'
    });
  }
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    success: false,
    message: 'Internal Server Error'
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'Route not found'
  });
});

app.listen(PORT, () => {
  console.log(`🚀 Minimal Server running on port ${PORT}`);
  console.log(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log(`🔗 API Base URL: http://localhost:${PORT}/api`);
});

module.exports = app;
