const express = require('express');
const { body, validationResult, query } = require('express-validator');
const Testimonial = require('../models/Testimonial');
const { authenticateToken, requireAdmin } = require('../middleware/auth');
const { uploadConfigs, optimizeImage, generateFileUrl } = require('../middleware/upload');

const router = express.Router();

// @route   GET /api/testimonials
// @desc    Get all testimonials
// @access  Public
router.get('/', [
  query('type').optional().isIn(['text', 'video']),
  query('active').optional().isBoolean(),
  query('project').optional().isMongoId()
], async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Invalid query parameters',
        errors: errors.array()
      });
    }

    const { type, active, project } = req.query;

    // Build filter object
    const filters = {};
    if (type) filters.type = type;
    if (active !== undefined) filters.isActive = active === 'true';
    else filters.isActive = true; // Default to active testimonials only
    if (project) filters.projectReference = project;

    const testimonials = await Testimonial.find(filters)
      .populate('projectReference', 'title category location')
      .select('-__v')
      .sort({ displayOrder: 1, createdAt: -1 });

    // Transform testimonials for frontend compatibility
    const transformedTestimonials = testimonials.map(testimonial => ({
      id: testimonial._id,
      name: testimonial.clientName,
      role: testimonial.clientDesignation,
      quote: testimonial.content,
      avatarSrc: testimonial.clientPhoto?.url,
      type: testimonial.type,
      videoSrc: testimonial.videoUrl,
      thumbnailSrc: testimonial.videoThumbnail?.url,
      rating: testimonial.rating,
      project: testimonial.projectReference,
      displayOrder: testimonial.displayOrder,
      createdAt: testimonial.createdAt
    }));

    res.json({
      success: true,
      data: {
        testimonials: transformedTestimonials
      }
    });

  } catch (error) {
    console.error('Get testimonials error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error fetching testimonials'
    });
  }
});

// @route   GET /api/testimonials/:id
// @desc    Get single testimonial by ID
// @access  Public
router.get('/:id', async (req, res) => {
  try {
    const testimonial = await Testimonial.findById(req.params.id)
      .populate('projectReference', 'title category location');

    if (!testimonial) {
      return res.status(404).json({
        success: false,
        message: 'Testimonial not found'
      });
    }

    res.json({
      success: true,
      data: {
        testimonial
      }
    });

  } catch (error) {
    console.error('Get testimonial error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error fetching testimonial'
    });
  }
});

// @route   POST /api/testimonials
// @desc    Create new testimonial
// @access  Private (Admin)
router.post('/', [
  authenticateToken,
  requireAdmin,
  uploadConfigs.testimonialMedia,
  optimizeImage,
  body('type').isIn(['text', 'video']).withMessage('Invalid testimonial type'),
  body('clientName').notEmpty().withMessage('Client name is required'),
  body('clientDesignation').optional().isLength({ max: 100 }).withMessage('Designation cannot exceed 100 characters'),
  body('content').if(body('type').equals('text')).notEmpty().withMessage('Content is required for text testimonials'),
  body('videoUrl').if(body('type').equals('video')).notEmpty().withMessage('Video URL is required for video testimonials'),
  body('rating').optional().isInt({ min: 1, max: 5 }).withMessage('Rating must be between 1 and 5'),
  body('projectReference').optional().isMongoId().withMessage('Invalid project reference'),
  body('displayOrder').optional().isInt({ min: 0 }).withMessage('Display order must be a positive integer')
], async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const testimonialData = { ...req.body };

    // Handle client photo if uploaded
    if (req.files && req.files.testimonialPhoto) {
      testimonialData.clientPhoto = {
        url: generateFileUrl(req, req.files.testimonialPhoto[0].filename, 'testimonials/'),
        filename: req.files.testimonialPhoto[0].filename
      };
    }

    // Handle video thumbnail if uploaded (for video testimonials)
    if (req.files && req.files.testimonialVideo && req.body.type === 'video') {
      testimonialData.videoThumbnail = {
        url: generateFileUrl(req, req.files.testimonialVideo[0].filename, 'testimonials/'),
        filename: req.files.testimonialVideo[0].filename
      };
    }

    const testimonial = new Testimonial(testimonialData);
    await testimonial.save();

    // Populate the created testimonial
    await testimonial.populate('projectReference', 'title category location');

    res.status(201).json({
      success: true,
      message: 'Testimonial created successfully',
      data: {
        testimonial
      }
    });

  } catch (error) {
    console.error('Create testimonial error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error creating testimonial'
    });
  }
});

// @route   PUT /api/testimonials/:id
// @desc    Update testimonial
// @access  Private (Admin)
router.put('/:id', [
  authenticateToken,
  requireAdmin,
  uploadConfigs.testimonialMedia,
  optimizeImage,
  body('type').optional().isIn(['text', 'video']).withMessage('Invalid testimonial type'),
  body('clientName').optional().notEmpty().withMessage('Client name cannot be empty'),
  body('clientDesignation').optional().isLength({ max: 100 }).withMessage('Designation cannot exceed 100 characters'),
  body('content').optional().isLength({ max: 1000 }).withMessage('Content cannot exceed 1000 characters'),
  body('rating').optional().isInt({ min: 1, max: 5 }).withMessage('Rating must be between 1 and 5'),
  body('projectReference').optional().isMongoId().withMessage('Invalid project reference'),
  body('displayOrder').optional().isInt({ min: 0 }).withMessage('Display order must be a positive integer'),
  body('isActive').optional().isBoolean().withMessage('isActive must be a boolean')
], async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const testimonial = await Testimonial.findById(req.params.id);
    if (!testimonial) {
      return res.status(404).json({
        success: false,
        message: 'Testimonial not found'
      });
    }

    const updateData = { ...req.body };

    // Handle client photo update if new file is uploaded
    if (req.files && req.files.testimonialPhoto) {
      updateData.clientPhoto = {
        url: generateFileUrl(req, req.files.testimonialPhoto[0].filename, 'testimonials/'),
        filename: req.files.testimonialPhoto[0].filename
      };
    }

    // Handle video thumbnail update if new file is uploaded
    if (req.files && req.files.testimonialVideo) {
      updateData.videoThumbnail = {
        url: generateFileUrl(req, req.files.testimonialVideo[0].filename, 'testimonials/'),
        filename: req.files.testimonialVideo[0].filename
      };
    }

    const updatedTestimonial = await Testimonial.findByIdAndUpdate(
      req.params.id,
      updateData,
      { new: true, runValidators: true }
    ).populate('projectReference', 'title category location');

    res.json({
      success: true,
      message: 'Testimonial updated successfully',
      data: {
        testimonial: updatedTestimonial
      }
    });

  } catch (error) {
    console.error('Update testimonial error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error updating testimonial'
    });
  }
});

// @route   DELETE /api/testimonials/:id
// @desc    Delete testimonial (soft delete)
// @access  Private (Admin)
router.delete('/:id', [
  authenticateToken,
  requireAdmin
], async (req, res) => {
  try {
    const testimonial = await Testimonial.findById(req.params.id);
    if (!testimonial) {
      return res.status(404).json({
        success: false,
        message: 'Testimonial not found'
      });
    }

    // Soft delete by setting isActive to false
    testimonial.isActive = false;
    await testimonial.save();

    res.json({
      success: true,
      message: 'Testimonial deleted successfully'
    });

  } catch (error) {
    console.error('Delete testimonial error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error deleting testimonial'
    });
  }
});

// @route   PUT /api/testimonials/reorder
// @desc    Update display order of testimonials
// @access  Private (Admin)
router.put('/reorder', [
  authenticateToken,
  requireAdmin,
  body('testimonialIds').isArray().withMessage('testimonialIds must be an array'),
  body('testimonialIds.*').isMongoId().withMessage('Invalid testimonial ID')
], async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { testimonialIds } = req.body;

    // Update display order
    await Testimonial.updateDisplayOrder(testimonialIds);

    res.json({
      success: true,
      message: 'Testimonial order updated successfully'
    });

  } catch (error) {
    console.error('Reorder testimonials error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error reordering testimonials'
    });
  }
});

module.exports = router;
