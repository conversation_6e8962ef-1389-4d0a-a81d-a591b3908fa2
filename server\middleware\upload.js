const multer = require('multer');
const sharp = require('sharp');
const path = require('path');
const fs = require('fs').promises;
const { v4: uuidv4 } = require('uuid');

// Ensure upload directories exist
const ensureDirectoryExists = async (dirPath) => {
  try {
    await fs.access(dirPath);
  } catch (error) {
    await fs.mkdir(dirPath, { recursive: true });
  }
};

// Configure storage
const storage = multer.diskStorage({
  destination: async (req, file, cb) => {
    let uploadPath = 'server/uploads/';
    
    // Determine upload path based on file type and route
    if (file.fieldname === 'coverImage' || file.fieldname === 'galleryImages') {
      uploadPath += 'projects/';
    } else if (file.fieldname === 'amenityIcon') {
      uploadPath += 'amenities/';
    } else if (file.fieldname === 'testimonialPhoto' || file.fieldname === 'testimonialVideo') {
      uploadPath += 'testimonials/';
    } else if (file.fieldname === 'brochure') {
      uploadPath += 'brochures/';
    } else if (file.fieldname === 'reraDocuments') {
      uploadPath += 'rera/';
    } else {
      uploadPath += 'misc/';
    }
    
    await ensureDirectoryExists(uploadPath);
    cb(null, uploadPath);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = uuidv4();
    const extension = path.extname(file.originalname);
    cb(null, `${uniqueSuffix}${extension}`);
  }
});

// File filter function
const fileFilter = (req, file, cb) => {
  // Define allowed file types
  const allowedImageTypes = /jpeg|jpg|png|webp|svg/;
  const allowedDocumentTypes = /pdf|doc|docx/;
  const allowedVideoTypes = /mp4|webm|avi|mov/;
  const allowedModelTypes = /glb|gltf|obj/;
  
  const extname = allowedImageTypes.test(path.extname(file.originalname).toLowerCase()) ||
                  allowedDocumentTypes.test(path.extname(file.originalname).toLowerCase()) ||
                  allowedVideoTypes.test(path.extname(file.originalname).toLowerCase()) ||
                  allowedModelTypes.test(path.extname(file.originalname).toLowerCase());
  
  const mimetype = file.mimetype.startsWith('image/') ||
                   file.mimetype.startsWith('video/') ||
                   file.mimetype === 'application/pdf' ||
                   file.mimetype === 'application/msword' ||
                   file.mimetype === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ||
                   file.mimetype === 'model/gltf-binary' ||
                   file.mimetype === 'application/octet-stream';

  if (mimetype && extname) {
    return cb(null, true);
  } else {
    cb(new Error('Invalid file type. Only images, documents, videos, and 3D models are allowed.'));
  }
};

// Configure multer
const upload = multer({
  storage: storage,
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB limit
    files: 10 // Maximum 10 files per request
  },
  fileFilter: fileFilter
});

// Image optimization middleware
const optimizeImage = async (req, res, next) => {
  if (!req.files && !req.file) {
    return next();
  }

  try {
    const files = req.files ? Object.values(req.files).flat() : [req.file];
    
    for (const file of files) {
      if (file && file.mimetype.startsWith('image/') && !file.mimetype.includes('svg')) {
        const optimizedPath = file.path.replace(path.extname(file.path), '_optimized.webp');
        
        await sharp(file.path)
          .resize(1920, 1080, { 
            fit: 'inside',
            withoutEnlargement: true 
          })
          .webp({ quality: 85 })
          .toFile(optimizedPath);
        
        // Replace original file with optimized version
        await fs.unlink(file.path);
        file.path = optimizedPath;
        file.filename = path.basename(optimizedPath);
        file.mimetype = 'image/webp';
      }
    }
    
    next();
  } catch (error) {
    console.error('Image optimization error:', error);
    next(); // Continue without optimization if it fails
  }
};

// Generate file URL
const generateFileUrl = (req, filename, folder = '') => {
  const baseUrl = `${req.protocol}://${req.get('host')}`;
  return `${baseUrl}/uploads/${folder}${filename}`;
};

// Delete file helper
const deleteFile = async (filePath) => {
  try {
    await fs.unlink(filePath);
  } catch (error) {
    console.error('Error deleting file:', error);
  }
};

// Middleware configurations for different upload types
const uploadConfigs = {
  single: (fieldName) => upload.single(fieldName),
  multiple: (fieldName, maxCount = 10) => upload.array(fieldName, maxCount),
  fields: (fields) => upload.fields(fields),
  
  // Specific configurations
  projectImages: upload.fields([
    { name: 'coverImage', maxCount: 1 },
    { name: 'galleryImages', maxCount: 20 },
    { name: 'brochure', maxCount: 1 },
    { name: 'reraDocuments', maxCount: 10 }
  ]),
  
  amenityIcon: upload.single('amenityIcon'),
  
  testimonialMedia: upload.fields([
    { name: 'testimonialPhoto', maxCount: 1 },
    { name: 'testimonialVideo', maxCount: 1 }
  ])
};

module.exports = {
  upload,
  uploadConfigs,
  optimizeImage,
  generateFileUrl,
  deleteFile,
  ensureDirectoryExists
};
