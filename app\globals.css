@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-poppins);
  --font-display: var(--font-playfair);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-highlight: var(--highlight);
  --color-highlight-alt: var(--highlight-alt);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --animate-ripple: ripple var(--duration,2s) ease calc(var(--i, 0)*.2s) infinite;
  @keyframes ripple {
  0%, 100% {
    transform: translate(-50%, -50%) scale(1);
    }
  50% {
    transform: translate(-50%, -50%) scale(0.9);
    }
  }
  --animate-shimmer-slide: shimmer-slide var(--speed) ease-in-out infinite alternate;
  --animate-spin-around: spin-around calc(var(--speed) * 2) infinite linear
;
  @keyframes shimmer-slide {
  to {
    transform: translate(calc(100cqw - 100%), 0);}}
  @keyframes spin-around {
  0% {
    transform: translateZ(0) rotate(0);}
  15%, 35% {
    transform: translateZ(0) rotate(90deg);}
  65%, 85% {
    transform: translateZ(0) rotate(270deg);}
  100% {
    transform: translateZ(0) rotate(360deg);}}}

:root {
  --radius: 0.625rem;

  /* Light theme variables */
  --background: #ffffff;
  --foreground: #1f2937;
  --card: #ffffff;
  --card-foreground: #1f2937;
  --popover: #ffffff;
  --popover-foreground: #1f2937;
  --primary: #f8f9fa;
  --primary-foreground: #1f2937;
  --secondary: #f3f4f6;
  --secondary-foreground: #1f2937;
  --muted: #f3f4f6;
  --muted-foreground: #6b7280;
  --accent: #2528c0;
  --accent-foreground: #ffffff;
  --destructive: oklch(0.577 0.245 27.325);
  --border: #e5e7eb;
  --input: #f3f4f6;
  --ring: #2528c0;
  --highlight: #2528c0;
  --highlight-rgb: 37, 40, 192;
  --highlight-alt: #6b7280;
  --sidebar: #ffffff;
  --sidebar-foreground: #1f2937;
  --sidebar-primary: #2528c0;
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: #f3f4f6;
  --sidebar-accent-foreground: #1f2937;
  --sidebar-border: #e5e7eb;
  --sidebar-ring: #2528c0;
}

.dark {
  --background: #1f2d3d;
  --foreground: #f5f5f5;
  --card: #2a3a4a;
  --card-foreground: #f5f5f5;
  --popover: #2a3a4a;
  --popover-foreground: #f5f5f5;
  --primary: #1f2d3d;
  --primary-foreground: #ffffff;
  --secondary: #3a4a5a;
  --secondary-foreground: #f5f5f5;
  --muted: #3a4a5a;
  --muted-foreground: #b1b1b1;
  --accent: #2528c0;
  --accent-foreground: #ffffff;
  --destructive: oklch(0.704 0.191 22.216);
  --border: rgba(245, 245, 245, 0.1);
  --input: rgba(245, 245, 245, 0.15);
  --ring: #2528c0;
  --highlight: #2528c0;
  --highlight-rgb: 37, 40, 192;
  --highlight-alt: #B1B1B1;
  --sidebar: #2a3a4a;
  --sidebar-foreground: #f5f5f5;
  --sidebar-primary: #2528c0;
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: #3a4a5a;
  --sidebar-accent-foreground: #f5f5f5;
  --sidebar-border: rgba(245, 245, 245, 0.1);
  --sidebar-ring: #2528c0;
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground font-sans;
  }
  h1, h2, h3, h4, h5, h6 {
    @apply font-display;
  }

  /* Smooth transitions for theme switching */
  html {
    transition: color 0.3s ease, background-color 0.3s ease;
  }

  html * {
    transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
  }
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Utility class to hide scrollbar */
.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

.scrollbar-hide {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}