"use client";

import { useEffect, useState } from 'react';
import { useParams, notFound } from 'next/navigation';
import { useProject } from '@/hooks/useProjects';
import { Loader2, ArrowLeft, MapPin, Calendar, Building, Users } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';
import { Button } from '@/components/ui/Button';
import { ProjectBrochureSection } from '@/components/ProjectBrochureSection';
import { AmenitiesFeatures } from '@/components/AmenitiesFeatures';
import AnimatedTitle from '@/components/ui/AnimatedTitle';

export default function ProjectDetailPage() {
  const params = useParams();
  const projectId = params.id as string;
  
  const { project, loading, error } = useProject(projectId);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <Loader2 size={48} className="mx-auto text-primary animate-spin mb-4" />
          <h3 className="text-xl font-semibold text-gray-700 dark:text-gray-300 mb-2">Loading Project...</h3>
          <p className="text-gray-500 dark:text-gray-400">
            Please wait while we fetch the project details.
          </p>
        </div>
      </div>
    );
  }

  if (error || !project) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-500 mb-4">
            <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h3 className="text-xl font-semibold text-gray-700 dark:text-gray-300 mb-2">Project Not Found</h3>
          <p className="text-gray-500 dark:text-gray-400 mb-4">
            {error || "The project you're looking for doesn't exist."}
          </p>
          <Link href="/projects">
            <Button variant="outline">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Projects
            </Button>
          </Link>
        </div>
      </div>
    );
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed": return "bg-green-500/90 text-white";
      case "ongoing": return "bg-blue-500/90 text-white";
      case "upcoming": return "bg-orange-500/90 text-white";
      default: return "bg-gray-500/90 text-white";
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case "residential": return "bg-purple-500/90 text-white";
      case "commercial": return "bg-teal-500/90 text-white";
      default: return "bg-gray-500/90 text-white";
    }
  };

  return (
    <main className="min-h-screen">
      {/* Back Navigation */}
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 pt-8">
        <Link href="/projects">
          <Button variant="ghost" className="mb-4">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Projects
          </Button>
        </Link>
      </div>

      {/* Hero Section */}
      <section className="py-12 md:py-20 bg-background">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 md:gap-12">
            {/* Project Image */}
            <div className="relative h-[350px] sm:h-[450px] md:h-[550px] lg:h-full min-h-[450px] rounded-lg shadow-xl group overflow-hidden">
              <Image
                src={project.imageSrc}
                alt={project.title}
                fill
                className="object-cover transition-transform duration-300 group-hover:scale-105"
                priority
              />
              <div className="absolute top-4 left-4 flex gap-2">
                <span className={`px-3 py-1 rounded-full text-sm font-semibold ${getCategoryColor(project.category)}`}>
                  {project.category.charAt(0).toUpperCase() + project.category.slice(1)}
                </span>
                <span className={`px-3 py-1 rounded-full text-sm font-semibold ${getStatusColor(project.status)}`}>
                  {project.status.charAt(0).toUpperCase() + project.status.slice(1)}
                </span>
              </div>
            </div>

            {/* Project Details */}
            <div className="flex flex-col justify-center">
              <AnimatedTitle
                text={project.title}
                className="text-3xl md:text-4xl lg:text-5xl font-display mb-4"
              />
              
              <div className="flex items-center text-foreground/70 mb-6">
                <MapPin className="h-5 w-5 mr-2" />
                <span className="text-lg">{project.location}</span>
              </div>

              <p className="text-lg text-foreground/80 mb-8 leading-relaxed">
                {project.description}
              </p>

              {/* Project Stats */}
              <div className="grid grid-cols-2 gap-4 mb-8">
                {project.specifications?.totalUnits && (
                  <div className="flex items-center">
                    <Building className="h-5 w-5 mr-2 text-primary" />
                    <div>
                      <p className="text-sm text-foreground/60">Total Units</p>
                      <p className="font-semibold">{project.specifications.totalUnits}</p>
                    </div>
                  </div>
                )}
                
                {project.specifications?.projectArea && (
                  <div className="flex items-center">
                    <Users className="h-5 w-5 mr-2 text-primary" />
                    <div>
                      <p className="text-sm text-foreground/60">Project Area</p>
                      <p className="font-semibold">{project.specifications.projectArea}</p>
                    </div>
                  </div>
                )}
              </div>

              {/* Pricing */}
              {project.pricing && (
                <div className="bg-primary/5 rounded-lg p-6 mb-8">
                  <h3 className="text-xl font-semibold mb-2">Pricing</h3>
                  <p className="text-2xl font-bold text-primary mb-2">
                    ₹{project.pricing.startingPrice} {project.pricing.priceUnit}
                  </p>
                  {project.pricing.priceDetails && (
                    <p className="text-foreground/70">{project.pricing.priceDetails}</p>
                  )}
                </div>
              )}

              {/* Custom Tags */}
              {project.customTags && project.customTags.length > 0 && (
                <div className="flex flex-wrap gap-2 mb-8">
                  {project.customTags.map((tag, index) => (
                    <span
                      key={index}
                      className="px-3 py-1 bg-secondary text-secondary-foreground rounded-full text-sm"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      </section>

      {/* Amenities Section */}
      {project.amenities && project.amenities.length > 0 && (
        <AmenitiesFeatures 
          amenities={project.amenities.map((amenity: any) => ({
            icon: amenity.icon?.url ? (
              <Image src={amenity.icon.url} alt={amenity.name} width={24} height={24} />
            ) : (
              <Building className="h-6 w-6" />
            ),
            title: amenity.name
          }))}
          projectName={project.title}
          projectId={project.id}
          brochureUrl={project.brochure?.url}
        />
      )}

      {/* Brochure Download Section */}
      {project.brochure?.url && (
        <section className="py-16 bg-muted/50">
          <ProjectBrochureSection 
            projectName={project.title}
            projectId={project.id}
            brochureUrl={project.brochure.url}
          />
        </section>
      )}

      {/* Specifications Section */}
      {project.specifications && (
        <section className="py-16">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <h2 className="text-3xl font-display text-center mb-12">Project Specifications</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {Object.entries(project.specifications).map(([key, value]) => (
                <div key={key} className="text-center p-6 bg-card rounded-lg shadow-sm">
                  <h3 className="text-lg font-semibold mb-2 capitalize">
                    {key.replace(/([A-Z])/g, ' $1').trim()}
                  </h3>
                  <p className="text-2xl font-bold text-primary">{value}</p>
                </div>
              ))}
            </div>
          </div>
        </section>
      )}
    </main>
  );
}
